import { Router, Request, Response } from 'express';
import { MyCoinNode } from '../core/MyCoinNode';
import { Wallet } from '../core/Wallet';
import * as crypto from 'crypto';
import * as bip39 from 'bip39';
import * as QRCode from 'qrcode';

// Utility function to get error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  return String(error);
}

// Utility function to validate wallet address
function isValidAddress(address: string): boolean {
  if (!address || address.length < 25 || address.length > 40) {
    return false;
  }
  
  // Basic validation for Base58 characters
  const base58Regex = /^[**********************************************************]+$/;
  return base58Regex.test(address);
}

export function walletRoutes(node: MyCoinNode): Router {
  const router = Router();

  /**
   * Tạo ví mới
   * POST /api/wallet/create
   */
  router.post('/create', async (req: Request, res: Response) => {
    try {
      const { password, saveToFile = false } = req.body;

      // Tạo ví mới
      const wallet = new Wallet();
      
      // Tạo mnemonic phrase
      const mnemonic = bip39.generateMnemonic();
      
      // Tạo QR code cho địa chỉ
      const qrCodeDataUrl = await QRCode.toDataURL(wallet.address);

      const response: any = {
        success: true,
        wallet: {
          address: wallet.address,
          publicKey: wallet.publicKey,
          mnemonic: mnemonic,
          qrCode: qrCodeDataUrl,
        },
        warnings: [
          'Never share your private key or mnemonic phrase with anyone',
          'Store your mnemonic phrase in a safe place',
          'MyCoin team will never ask for your private key',
          'Make sure to backup your wallet before proceeding'
        ]
      };

      // Nếu có password, mã hóa private key
      if (password) {
        const encryptedPrivateKey = wallet.encryptPrivateKey(password);
        response.wallet.encryptedPrivateKey = encryptedPrivateKey;
      }

      // Nếu yêu cầu lưu file
      if (saveToFile && password) {
        const walletData = wallet.saveToFile('', password);
        response.walletFile = {
          filename: `mycoin-wallet-${wallet.address.substring(0, 8)}.json`,
          data: walletData
        };
      }

      res.json(response);
    } catch (error) {
      console.error('Error creating wallet:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create wallet',
        message: getErrorMessage(error)
      });
    }
  });

  /**
   * Import ví từ private key hoặc mnemonic
   * POST /api/wallet/import
   */
  router.post('/import', async (req: Request, res: Response) => {
    try {
      const { privateKey, mnemonic, password, keystore } = req.body;

      let wallet: Wallet;

      if (privateKey) {
        // Import từ private key
        wallet = new Wallet(privateKey);
      } else if (mnemonic) {
        // Import từ mnemonic phrase
        if (!bip39.validateMnemonic(mnemonic)) {
          return res.status(400).json({
            success: false,
            error: 'Invalid mnemonic phrase'
          });
        }
        wallet = Wallet.fromMnemonic(mnemonic);
      } else if (keystore && password) {
        // Import từ keystore file
        try {
          const decryptedPrivateKey = Wallet.decryptPrivateKey(keystore.privateKey, password);
          wallet = new Wallet(decryptedPrivateKey);
        } catch (error) {
          return res.status(400).json({
            success: false,
            error: 'Invalid password or corrupted keystore file'
          });
        }
      } else {
        return res.status(400).json({
          success: false,
          error: 'Please provide private key, mnemonic phrase, or keystore file with password'
        });
      }

      // Lấy balance
      const balance = wallet.getBalance(node.getBlockchain());
      
      // Tạo QR code
      const qrCodeDataUrl = await QRCode.toDataURL(wallet.address);

      res.json({
        success: true,
        wallet: {
          address: wallet.address,
          publicKey: wallet.publicKey,
          balance: balance,
          qrCode: qrCodeDataUrl,
        }
      });
    } catch (error) {
      console.error('Error importing wallet:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to import wallet',
        message: getErrorMessage(error)
      });
    }
  });

  /**
   * Lấy thông tin ví
   * GET /api/wallet/:address/info
   */
  router.get('/:address/info', async (req: Request, res: Response) => {
    try {
      const { address } = req.params;

      // Validate address format
      if (!isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid wallet address format'
        });
      }

      const blockchain = node.getBlockchain();
      const balance = blockchain.getBalanceOfAddress(address);
      const utxos = blockchain.getUTXOsForAddress(address);
      
      // Tạo QR code
      const qrCodeDataUrl = await QRCode.toDataURL(address);

      res.json({
        success: true,
        wallet: {
          address: address,
          balance: balance,
          utxoCount: utxos.length,
          qrCode: qrCodeDataUrl,
        }
      });
    } catch (error) {
      console.error('Error getting wallet info:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get wallet information',
        message: getErrorMessage(error)
      });
    }
  });

  /**
   * Lấy số dư ví
   * GET /api/wallet/:address/balance
   */
  router.get('/:address/balance', (req: Request, res: Response) => {
    try {
      const { address } = req.params;

      // Validate address format
      if (!isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid wallet address format'
        });
      }

      const balance = node.getBlockchain().getBalanceOfAddress(address);

      res.json({
        success: true,
        address: address,
        balance: balance
      });
    } catch (error) {
      console.error('Error getting balance:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get balance',
        message: getErrorMessage(error)
      });
    }
  });

  /**
   * Lấy UTXO của ví
   * GET /api/wallet/:address/utxos
   */
  router.get('/:address/utxos', (req: Request, res: Response) => {
    try {
      const { address } = req.params;

      // Validate address format
      if (!isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid wallet address format'
        });
      }

      const utxos = node.getBlockchain().getUTXOsForAddress(address);

      res.json({
        success: true,
        address: address,
        utxos: utxos,
        totalAmount: utxos.reduce((sum, utxo) => sum + utxo.amount, 0)
      });
    } catch (error) {
      console.error('Error getting UTXOs:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get UTXOs',
        message: getErrorMessage(error)
      });
    }
  });

  /**
   * Validate địa chỉ ví
   * POST /api/wallet/validate
   */
  router.post('/validate', (req: Request, res: Response) => {
    try {
      const { address } = req.body;

      if (!address) {
        return res.status(400).json({
          success: false,
          error: 'Address is required'
        });
      }

      // Basic validation - trong thực tế sẽ có validation phức tạp hơn
      const isValid = typeof address === 'string' && 
                     address.length === 64 && 
                     /^[a-fA-F0-9]+$/.test(address);

      res.json({
        success: true,
        address: address,
        isValid: isValid
      });
    } catch (error) {
      console.error('Error validating address:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate address',
        message: getErrorMessage(error)
      });
    }
  });

  return router;
}

